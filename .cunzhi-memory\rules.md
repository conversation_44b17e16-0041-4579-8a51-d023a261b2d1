# 开发规范和规则

- 项目结构规范：后端模块化设计，jeecg-boot为后端源码，jeecgboot-vue3为前端源码。核心模块包括jeecg-boot-base-core（基础核心）、jeecg-module-system（系统模块）、jeecg-boot-module-airag（AI RAG模块）。前端采用src目录结构，包含api、components、views、utils等标准目录。
- API接口规范：基础路径/jeecg-boot，采用RESTful设计，JWT Token认证，JSON响应格式。AI相关接口路径为/airag，包括知识库管理(/airag/knowledge)、聊天(/airag/chat)、文档处理等。统一使用defHttp进行HTTP请求处理。
- 前端开发规范：使用TypeScript严格模式，路径别名@/*指向src/*，组件采用Vue3 Composition API。UI组件基于Ant Design Vue 4，自定义组件放在components目录。页面路由配置在router目录，状态管理使用Pinia。
- 后端开发规范：使用Maven管理依赖，包结构org.jeecg.modules，实体类使用MyBatis-Plus注解。服务层采用@Service注解，控制器使用@RestController。数据库操作通过Mapper接口，支持多数据源配置。权限控制基于Shiro和JWT。
- 配置管理规范：主配置文件application.yml，环境配置application-{profile}.yml。服务端口8080，上下文路径/jeecg-boot。支持Docker部署，配置文件支持多环境切换（dev、test、prod、docker）。数据库配置支持动态数据源。
- 代码生成器规范：提供强大的代码生成器，支持单表、树列表、一对多等数据模型。模板机制支持自定义，提供四套风格模板。生成的代码包含增删改查功能，支持Excel导入导出。Online表单支持零代码开发。
- 文档知识库设计规范：基于knowledge-base-design.md设计方案，包含文档管理、知识库管理、智能搜索、AI增强功能四大模块。数据库表设计包括kb_document_library（文档库）、kb_document（文档）等核心表。支持多格式文档解析和向量化处理。
- 数据库初始化文件管理规范：每开发新模块前必须更新数据库初始化文件，包含1)数据库表结构 2)权限配置 3)角色权限关联 4)测试数据。插入的权限、角色、测试数据格式必须与当前数据库表字段完全一致，必要时查看本地数据库确认字段结构。
- 接口测试规范：每开发完一个模块后，必须使用curl.exe对该模块controller中所有接口进行测试。测试格式：curl.exe -X POST "http://localhost:8080/jeecg-boot/模块路径/接口" -H "Content-Type: application/json" -H "X-Access-Token: [token]" -d '{json数据}'。必须等待用户手动启动服务并提供正确的JWT token，不要自己假设token或在服务未启动时测试。
- 模块开发代码规范：严格符合JeecgBoot框架的代码开发规范，不要盲目猜测。必须参考现有模块的代码结构、注解使用（swagger3）、命名规范等。开发前要仔细研究现有模块的实现方式，确保代码风格和架构的一致性。
- 模块关联接口补齐规范：新模块开发完成后，需要检查之前已开发的模块是否有预留的待开发接口（如关联查询、业务流程接口等），如果有则需要补齐这些接口的实现。确保模块间的业务关联完整性。
