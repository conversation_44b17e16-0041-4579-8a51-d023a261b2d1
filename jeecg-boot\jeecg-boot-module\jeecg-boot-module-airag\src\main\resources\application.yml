server:
  port: 7008
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
      - org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 1000
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        #        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        # !!!!!mysql
        #        filters: stat,slf4j,wall
        # !!!!!DM
        filters: stat,slf4j
        # 允许SELECT语句的WHERE子句是一个永真条件
        #        wall:
        #          selectWhereAlwayTrueCheck: false
        # 打开mergeSql功能；慢SQL记录
        stat:
          merge-sql: true
          slow-sql-millis: 5000
      datasource:
        master:
          ## !!!!!MYSQL
          url: *****************************************************************************************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    database: 0
    host: *************
    port: 6379
    password: 'res983'
jeecg:
  ai-rag:
    embed-store:
      host: "localhost"
      port: 15432
      database: "postgres"
      user: "postgres"
      password: "123456"
      table: "embeddings"
