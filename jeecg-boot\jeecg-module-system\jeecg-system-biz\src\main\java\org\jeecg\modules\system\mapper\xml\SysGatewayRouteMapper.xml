<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysGatewayRouteMapper">

    <!-- 还原被逻辑删除的路由 -->
    <update id="revertLogicDeleted">
        UPDATE
        sys_gateway_route
        SET
        del_flag = 0
        WHERE
        del_flag = 1
        AND id IN
        <foreach collection="ids" item="routeId" open="(" close=")" separator="," >
            #{routeId}
        </foreach>
    </update>

    <!-- 彻底删除路由 -->
    <delete id="deleteLogicDeleted">
        DELETE FROM sys_gateway_route
        WHERE
        del_flag = 1
        AND id IN
        <foreach collection="ids" item="routeId" open="(" close=")" separator="," >
            #{routeId}
        </foreach>
    </delete>

</mapper>