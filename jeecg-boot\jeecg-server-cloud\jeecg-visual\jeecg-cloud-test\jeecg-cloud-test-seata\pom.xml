<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-cloud-test</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.8.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jeecg-cloud-test-seata</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>jeecg-cloud-test-seata-account</module>
        <module>jeecg-cloud-test-seata-product</module>
        <module>jeecg-cloud-test-seata-order</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-cloud</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-seata</artifactId>
            <version>${jeecgboot.version}</version>
        </dependency>
    </dependencies>
</project>
