package ${bussiPackage}.${entityPackage}.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@TableName("${tableName}")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="${tableVo.ftlDescription}")
public class ${entityName} {
    
    <#list originalColumns as po>
	/**${po.filedComment}*/
	<#if po.fieldName == primaryKeyField>
	@TableId(type = IdType.ASSIGN_ID)
	<#else>
  <#if po.fieldType =='java.util.Date'>
	<#if po.fieldDbType =='date'>
	@Excel(name = "${po.filedComment}", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	<#elseif po.fieldDbType =='datetime'>
	@Excel(name = "${po.filedComment}", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	</#if>
	<#else>
	@Excel(name = "${po.filedComment}", width = 15)
  </#if>
  </#if>
    @Schema(description = "${po.filedComment}")
	private <#if po.fieldType=='java.sql.Blob'>byte[]<#else>${po.fieldType}</#if> ${po.fieldName};
	</#list>
}
